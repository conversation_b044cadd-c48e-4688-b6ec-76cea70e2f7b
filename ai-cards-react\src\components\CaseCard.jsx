import React, { useState } from 'react'

const CaseCard = ({ 
  title, 
  tags, 
  tagColors, 
  headerColor, 
  description, 
  features, 
  expandedContent, 
  releaseDate, 
  buttonColor 
}) => {
  const [isExpanded, setIsExpanded] = useState(false)

  return (
    <div className="card hover-effect animate-on-scroll">
      <div className={`card-header ${headerColor}`}>
        <h5 className="card-title mb-0">{title}</h5>
      </div>
      <div className="card-body">
        {tags.map((tag, index) => (
          <span key={index} className={`tag ${tagColors[index]}`}>
            {tag}
          </span>
        ))}
        
        <p className="mt-3">{description}</p>
        
        <div className="mt-3">
          <strong>核心特点：</strong>
          <ul>
            {features.map((feature, index) => (
              <li key={index}>{feature}</li>
            ))}
          </ul>
        </div>
        
        <div className={`expandable-content ${isExpanded ? 'show' : ''}`}>
          {expandedContent.map((paragraph, index) => (
            <p key={index}>{paragraph}</p>
          ))}
        </div>
        
        <button 
          className={`btn btn-sm ${buttonColor} mt-3 expand-btn`}
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? '收起' : '查看更多'}
        </button>
      </div>
      <div className="card-footer text-muted">
        <small>发布日期：{releaseDate}</small>
      </div>
    </div>
  )
}

export default CaseCard
