import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'

// Import Bootstrap CSS
import 'bootstrap/dist/css/bootstrap.min.css'
import 'bootstrap-icons/font/bootstrap-icons.css'

// Render the main app
createRoot(document.getElementById('root')).render(
  <StrictMode>
    <App />
  </StrictMode>,
)

// Initialize Stagewise toolbar separately (development only)
if (import.meta.env.DEV) {
  console.log('🔧 Development mode detected - attempting to load Stagewise toolbar...')

  try {
    // Try to import and initialize Stagewise toolbar
    import('@stagewise/toolbar-react').then(({ StagewiseToolbar }) => {
      console.log('✅ Stagewise toolbar module loaded successfully')

      const toolbarConfig = {
        plugins: [], // Add your custom plugins here
      }

      // Create toolbar container
      const toolbarRoot = document.createElement('div')
      toolbarRoot.id = 'stagewise-toolbar-root'
      toolbarRoot.style.position = 'fixed'
      toolbarRoot.style.top = '0'
      toolbarRoot.style.left = '0'
      toolbarRoot.style.zIndex = '10000'
      document.body.appendChild(toolbarRoot)

      // Render toolbar
      createRoot(toolbarRoot).render(
        <StrictMode>
          <StagewiseToolbar config={toolbarConfig} />
        </StrictMode>
      )

      console.log('🎉 Stagewise toolbar initialized successfully!')
    }).catch(error => {
      console.warn('⚠️ Failed to load Stagewise toolbar:', error)
      console.log('💡 This is normal if the Stagewise package is not properly installed or the VS Code extension is not running.')
    })
  } catch (error) {
    console.warn('⚠️ Stagewise toolbar not available:', error)
  }
}
