import React from 'react'

const Header = ({ darkMode, setDarkMode, searchTerm, setSearchTerm }) => {
  return (
    <header className="header py-3 text-white">
      <div className="container d-flex justify-content-between align-items-center">
        <h1 className="m-0">AI资讯知识卡片</h1>
        <div className="d-flex align-items-center">
          <div className="form-check form-switch me-3">
            <input 
              className="form-check-input" 
              type="checkbox" 
              id="darkModeToggle"
              checked={darkMode}
              onChange={(e) => setDarkMode(e.target.checked)}
            />
            <label className="form-check-label text-white" htmlFor="darkModeToggle">
              暗色模式
            </label>
          </div>
          <div className="search-container">
            <input 
              type="text" 
              id="searchInput" 
              className="form-control" 
              placeholder="搜索..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
