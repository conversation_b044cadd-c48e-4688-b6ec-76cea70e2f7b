// Stagewise toolbar configuration and initialization
// This file handles the development-only loading of the stagewise toolbar

// Basic stagewise configuration
const stagewiseConfig = {
  plugins: [],
  experimental: {
    enableStagewiseMCP: false,
    enableToolCalls: false
  }
};

// Function to initialize stagewise toolbar
function initStagewise() {
  // Only initialize in development mode
  // For static HTML, we'll check for localhost or development indicators
  const isDevelopment =
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1' ||
    window.location.port !== '' ||
    window.location.protocol === 'file:';

  console.log('Stagewise initialization check:', {
    hostname: window.location.hostname,
    port: window.location.port,
    protocol: window.location.protocol,
    isDevelopment: isDevelopment
  });

  if (isDevelopment) {
    console.log('Loading Stagewise toolbar...');
    try {
      // Import and initialize the stagewise toolbar
      import('./node_modules/@stagewise/toolbar/dist/index.es.js')
        .then((module) => {
          console.log('Stagewise module loaded:', module);
          const { initToolbar } = module;
          if (typeof initToolbar === 'function') {
            console.log('Initializing Stagewise toolbar with config:', stagewiseConfig);
            initToolbar(stagewiseConfig);
            console.log('Stagewise toolbar initialized successfully!');
          } else {
            console.error('initToolbar is not a function:', typeof initToolbar);
          }
        })
        .catch(error => {
          console.error('Failed to load Stagewise toolbar:', error);
        });
    } catch (error) {
      console.error('Stagewise toolbar not available:', error);
    }
  } else {
    console.log('Stagewise toolbar disabled in production mode');
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initStagewise);
} else {
  initStagewise();
}
