// Stagewise toolbar configuration and initialization
// This file handles the development-only loading of the stagewise toolbar

// Basic stagewise configuration
const stagewiseConfig = {
  plugins: []
};

// Function to initialize stagewise toolbar
function initStagewise() {
  // Only initialize in development mode
  // For static HTML, we'll check for localhost or development indicators
  const isDevelopment = 
    window.location.hostname === 'localhost' || 
    window.location.hostname === '127.0.0.1' ||
    window.location.port !== '' ||
    window.location.protocol === 'file:';

  if (isDevelopment) {
    try {
      // Import and initialize the stagewise toolbar
      import('./node_modules/@stagewise/toolbar/dist/index.es.js')
        .then(({ initToolbar }) => {
          console.log('Initializing Stagewise toolbar...');
          initToolbar(stagewiseConfig);
        })
        .catch(error => {
          console.warn('Failed to load Stagewise toolbar:', error);
        });
    } catch (error) {
      console.warn('Stagewise toolbar not available:', error);
    }
  } else {
    console.log('Stagewise toolbar disabled in production mode');
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initStagewise);
} else {
  initStagewise();
}
