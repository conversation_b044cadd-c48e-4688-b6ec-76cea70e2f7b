// Stagewise toolbar configuration and initialization
// This file handles the development-only loading of the stagewise toolbar

// Polyfill for process.env in browser environment
if (typeof window !== 'undefined' && !window.process) {
  window.process = {
    env: {
      NODE_ENV: window.location.hostname === 'localhost' ||
                window.location.hostname === '127.0.0.1' ||
                window.location.port !== '' ? 'development' : 'production'
    }
  };
}

// Basic stagewise configuration
const stagewiseConfig = {
  plugins: [],
  experimental: {
    enableStagewiseMCP: false,
    enableToolCalls: false
  }
};

// Function to initialize stagewise toolbar
function initStagewise() {
  // Only initialize in development mode
  // For static HTML, we'll check for localhost or development indicators
  const isDevelopment =
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1' ||
    window.location.port !== '' ||
    window.location.protocol === 'file:';

  console.log('Stagewise initialization check:', {
    hostname: window.location.hostname,
    port: window.location.port,
    protocol: window.location.protocol,
    isDevelopment: isDevelopment
  });

  if (isDevelopment) {
    console.log('Loading Stagewise toolbar...');

    // 首先加载 CSS
    const cssLink = document.createElement('link');
    cssLink.rel = 'stylesheet';
    cssLink.href = './node_modules/@stagewise/toolbar/dist/toolbar.css';
    cssLink.onload = () => console.log('Stagewise CSS loaded successfully');
    cssLink.onerror = () => console.warn('Failed to load Stagewise CSS');
    document.head.appendChild(cssLink);

    try {
      // Import and initialize the stagewise toolbar
      import('./node_modules/@stagewise/toolbar/dist/index.es.js')
        .then((module) => {
          console.log('Stagewise module loaded:', module);
          const { initToolbar } = module;
          if (typeof initToolbar === 'function') {
            console.log('Initializing Stagewise toolbar with config:', stagewiseConfig);
            initToolbar(stagewiseConfig);
            console.log('Stagewise toolbar initialized successfully!');

            // 添加一个提示，告诉用户工具栏已加载
            setTimeout(() => {
              console.log('🎉 Stagewise toolbar should now be visible on the page!');
              console.log('Look for a floating toolbar or button on the page.');
              console.log('💡 Make sure you have the Stagewise VS Code extension installed and running.');
            }, 1000);
          } else {
            console.error('initToolbar is not a function:', typeof initToolbar);
          }
        })
        .catch(error => {
          console.error('Failed to load Stagewise toolbar:', error);
          console.log('📋 Troubleshooting tips:');
          console.log('1. Make sure the Stagewise VS Code extension is installed');
          console.log('2. Check if you\'re running a local development server');
          console.log('3. Verify the toolbar package is properly installed');
        });
    } catch (error) {
      console.error('Stagewise toolbar not available:', error);
    }
  } else {
    console.log('Stagewise toolbar disabled in production mode');
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initStagewise);
} else {
  initStagewise();
}
