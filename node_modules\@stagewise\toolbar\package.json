{"name": "@stagewise/toolbar", "private": false, "version": "0.4.8", "type": "module", "description": "stagewise toolbar SDK for AI Agent interaction.", "keywords": ["stagewise", "toolbar", "ai", "devtool", "agent", "interaction"], "author": "tiq UG (haftungsbeschränkt)", "homepage": "https://stagewise.io", "bugs": {"url": "https://github.com/stagewise-io/stagewise/issues"}, "repository": {"type": "git", "url": "https://github.com/stagewise-io/stagewise.git", "directory": "packages/toolbar"}, "publishConfig": {"access": "public"}, "types": "./dist/index.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.umd.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.umd.js"}, "./plugin-ui": {"types": "./dist/plugin-ui.d.ts", "import": "./dist/plugin-ui.es.js", "require": "./dist/plugin-ui.umd.js"}, "./plugin-ui/jsx-runtime": {"types": "./dist/plugin-ui/jsx-runtime.d.ts", "import": "./dist/plugin-ui/jsx-runtime.es.js", "require": "./dist/plugin-ui/jsx-runtime.umd.js"}}, "files": ["dist"], "license": "AGPL-3.0-only", "devDependencies": {"@headlessui/react": "2.2.2", "@preact/compat": "18.3.1", "@preact/preset-vite": "^2.10.1", "@tailwindcss/postcss": "^4.1.5", "@types/node": "22.15.2", "autoprefixer": "^10.4.21", "bowser": "^2.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.503.0", "postcss": "^8.5.3", "postcss-prefix-selector": "^2.1.1", "preact": "^10.26.6", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.5", "tsup": "^8.4.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-bundle-analyzer": "^0.19.0", "vite-plugin-css-injected-by-js": "^3.5.2", "vite-plugin-dts": "^4.5.3", "zod": "^3.24.4", "@stagewise/extension-toolbar-srpc-contract": "0.1.3", "@stagewise/srpc": "0.2.1"}, "scripts": {"clean": "rm -rf .turbo node_modules", "dev": "tsc -b && vite build --mode development", "dev:examples": "tsc -b && vite build --mode development", "build:toolbar": "tsc -b && vite build --mode production", "build": "tsc -b && vite build --mode production"}}