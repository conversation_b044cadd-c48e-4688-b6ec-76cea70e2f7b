document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    initTooltips();
    
    // 平滑滚动
    initSmoothScrolling();
    
    // 卡片过滤功能
    initCardFiltering();
    
    // 返回顶部按钮
    initBackToTopButton();
    
    // 暗色模式切换
    initDarkModeToggle();
    
    // 可展开内容
    initExpandableContent();
    
    // 搜索功能
    initSearch();
    
    // 滚动动画
    initScrollAnimations();
});

// 初始化工具提示
function initTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// 平滑滚动
function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
}

// 卡片过滤功能
function initCardFiltering() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const cards = document.querySelectorAll('.card[data-categories]');
    
    if (filterButtons.length > 0) {
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');
                
                // 更新活跃按钮
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                // 过滤卡片
                cards.forEach(card => {
                    const categories = card.getAttribute('data-categories').split(',');
                    
                    if (filter === 'all' || categories.includes(filter)) {
                        card.style.display = '';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });
        
        // 默认点击"全部"按钮
        const allButton = document.querySelector('.filter-btn[data-filter="all"]');
        if (allButton) {
            allButton.classList.add('active');
        }
    }
}

// 返回顶部按钮
function initBackToTopButton() {
    const backToTopButton = document.querySelector('.back-to-top');
    
    if (backToTopButton) {
        // 监听滚动事件
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.add('visible');
            } else {
                backToTopButton.classList.remove('visible');
            }
        });
        
        // 点击事件
        backToTopButton.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

// 暗色模式切换
function initDarkModeToggle() {
    const darkModeToggle = document.getElementById('darkModeToggle');
    
    if (darkModeToggle) {
        // 检查本地存储中的主题设置
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.documentElement.setAttribute('data-theme', 'dark');
            darkModeToggle.checked = true;
        }
        
        // 切换事件
        darkModeToggle.addEventListener('change', function() {
            if (this.checked) {
                document.documentElement.setAttribute('data-theme', 'dark');
                localStorage.setItem('theme', 'dark');
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('theme', 'light');
            }
        });
    }
}

// 可展开内容
function initExpandableContent() {
    const expandButtons = document.querySelectorAll('.expand-btn');
    
    expandButtons.forEach(button => {
        button.addEventListener('click', function() {
            const cardBody = this.closest('.card-body');
            const expandableContent = cardBody.querySelector('.expandable-content');
            
            if (expandableContent) {
                if (expandableContent.style.display === 'block') {
                    expandableContent.style.display = 'none';
                    this.textContent = '查看更多';
                } else {
                    expandableContent.style.display = 'block';
                    this.textContent = '收起';
                }
            }
        });
    });
}

// 搜索功能
function initSearch() {
    const searchInput = document.getElementById('searchInput');
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const cards = document.querySelectorAll('.card');
            let activeFilter = document.querySelector('.filter-btn.active');
            
            cards.forEach(card => {
                const cardText = card.textContent.toLowerCase();
                const cardTitle = card.querySelector('.card-title')?.textContent.toLowerCase() || '';
                const cardBody = card.querySelector('.card-body')?.textContent.toLowerCase() || '';
                
                // 如果搜索词为空，恢复活跃过滤器的状态
                if (searchTerm === '') {
                    if (activeFilter && card.hasAttribute('data-categories')) {
                        const filter = activeFilter.getAttribute('data-filter');
                        const categories = card.getAttribute('data-categories').split(',');
                        
                        if (filter === 'all' || categories.includes(filter)) {
                            card.style.display = '';
                        } else {
                            card.style.display = 'none';
                        }
                    } else {
                        card.style.display = '';
                    }
                    return;
                }
                
                // 搜索匹配
                if (cardText.includes(searchTerm) || cardTitle.includes(searchTerm) || cardBody.includes(searchTerm)) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    }
}

// 滚动动画
function initScrollAnimations() {
    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    
    function checkIfInView() {
        animatedElements.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementVisible = 150;
            
            if (elementTop < window.innerHeight - elementVisible) {
                element.classList.add('visible');
            }
        });
    }
    
    // 初始检查
    checkIfInView();
    
    // 滚动时检查
    window.addEventListener('scroll', checkIfInView);
}