/* 全局样式 */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --body-bg: #f8f9fa;
    --card-bg: #ffffff;
    --text-color: #212529;
    --header-bg: linear-gradient(135deg, #0d6efd, #0a58ca);
    --footer-bg: #343a40;
    --footer-text: #f8f9fa;
    --transition-speed: 0.3s;
}

/* 暗色模式 */
[data-theme="dark"] {
    --body-bg: #121212;
    --card-bg: #1e1e1e;
    --text-color: #e0e0e0;
    --header-bg: linear-gradient(135deg, #0a4b9c, #072c5e);
    --footer-bg: #1a1a1a;
    --footer-text: #f8f9fa;
    --border-color: #333;
    --hover-color: #2c2c2c;
    --shadow-color: rgba(0, 0, 0, 0.2);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--body-bg);
    color: var(--text-color);
    transition: background-color var(--transition-speed), color var(--transition-speed);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* 头部样式 */
.header {
    background: var(--header-bg);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header h1 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.search-container {
    position: relative;
    max-width: 300px;
    width: 100%;
}

.search-input {
    border-radius: 20px;
    padding-left: 2.5rem;
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
}

/* 暗色模式切换 */
.dark-mode-toggle {
    width: 50px;
    height: 26px;
    border-radius: 13px;
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    cursor: pointer;
    transition: background-color var(--transition-speed);
}

.toggle-thumb {
    width: 20px;
    height: 20px;
    background-color: white;
    border-radius: 50%;
    position: absolute;
    top: 2px;
    left: 3px;
    transition: transform var(--transition-speed);
}

[data-theme="dark"] .toggle-thumb {
    transform: translateX(24px);
}

/* 卡片样式 */
.card {
    background-color: var(--card-bg);
    border: 1px solid rgba(0, 0, 0, 0.05);
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    height: 100%;
    margin-bottom: 1.5rem;
}

.hover-effect:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
    background-color: rgba(0, 0, 0, 0.02);
}

.card-footer {
    background-color: transparent;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* 标签样式 */
.tag {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.tag-voice {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
}

.tag-research {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
}

.tag-hardware {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.tag-medical {
    background-color: rgba(13, 202, 240, 0.1);
    color: #0dcaf0;
}

/* 可展开内容 */
.expandable-content {
    display: none;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    margin-top: 1rem;
}

.expand-btn {
    color: var(--primary-color);
    background: none;
    border: none;
    padding: 0;
    font-size: 0.9rem;
    cursor: pointer;
}

.expand-btn:hover {
    text-decoration: underline;
}

/* 趋势项目 */
.trend-item {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.trend-item:last-child {
    border-bottom: none;
}

.trend-icon {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.trend-up {
    color: #28a745;
}

.trend-down {
    color: #dc3545;
}

.trend-neutral {
    color: #ffc107;
}

/* 页脚样式 */
.footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
    padding: 3rem 0 1.5rem;
    margin-top: 3rem;
}

.footer h5 {
    color: white;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.footer ul {
    list-style: none;
    padding-left: 0;
}

.footer ul li {
    margin-bottom: 0.5rem;
}

.footer a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color var(--transition-speed);
}

.footer a:hover {
    color: white;
    text-decoration: underline;
}

/* 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity var(--transition-speed);
    z-index: 999;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.back-to-top.visible {
    opacity: 1;
}

/* 动画效果 */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .header h1 {
        font-size: 1.5rem;
    }
    
    .filter-btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .card-title {
        font-size: 1.25rem;
    }
}

/* 表格响应式 */
.table-responsive {
    border-radius: 8px;
    overflow-x: auto;
}

.table th {
    background-color: rgba(0, 0, 0, 0.03);
    white-space: nowrap;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* 标签页样式 */
.nav-tabs {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.nav-tabs .nav-link {
    color: var(--text-color);
    border: none;
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    transition: color var(--transition-speed);
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    border-bottom: 3px solid var(--primary-color);
    background-color: transparent;
}

.nav-tabs .nav-link:hover:not(.active) {
    border-bottom: 3px solid rgba(13, 110, 253, 0.3);
}

/* 过滤按钮样式 */
.filter-btn {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    border-radius: 20px;
    padding: 0.375rem 0.75rem;
}

.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
}