# AI资讯收集与分析工具

## 项目简介

这是一个使用Playwright自动化工具收集并分析AI行业最新资讯的项目。该工具可以自动访问AI资讯网站，提取最新的AI相关新闻、产品发布、融资信息等，并将其整理成结构化的Markdown文档，同时提供深度分析报告，方便用户快速了解AI行业动态和发展趋势。

## 功能特点

- **自动化数据采集**：使用Playwright自动访问AI资讯网站，无需手动操作
- **内容智能提取**：自动识别并提取最新的AI资讯内容，包括标题、正文和来源
- **结构化输出**：将采集的资讯整理成规范的Markdown格式，便于阅读和分享
- **深度趋势分析**：对采集的资讯进行背景、原因、影响和发展趋势的结构化分析
- **定制化采集**：可以根据需要调整采集的资讯数量和类型

## 使用方法

### 前提条件

- 安装了Node.js环境
- 安装了Playwright库
- 配置了MCP (Model Control Protocol) 环境

### 基本使用

1. 克隆或下载本项目到本地
2. 在项目根目录下运行以下命令：

```
# 采集最新的AI资讯并保存到Markdown文件
# 示例命令（实际命令可能因环境而异）
```

3. 执行完成后，将在项目目录下生成`AI资讯汇总.md`文件，包含最新的AI资讯内容
4. 同时生成`AI资讯分析报告.md`文件，提供对资讯的深度分析和趋势洞察

### 自定义配置

可以通过修改参数来自定义资讯采集：

- **资讯数量**：可以调整采集的资讯数量，默认为5篇
- **资讯来源**：目前默认从AI工具集网站采集，可以扩展支持其他来源
- **输出格式**：可以调整输出的Markdown格式，满足不同的展示需求
- **分析维度**：可以自定义分析报告的维度，如技术、市场、投资等

## 项目结构

```
├── README.md                 # 项目说明文档
├── AI资讯汇总.md             # 生成的AI资讯汇总文件
├── AI资讯分析报告.md         # 生成的AI资讯深度分析报告
```

## 分析报告内容

分析报告采用结构化思维方法，包含以下主要部分：

- **个案分析**：对每篇资讯的背景、原因、影响和发展趋势进行深入分析
- **横向对比**：通过对比不同资讯，发现共同趋势和规律
- **整体趋势**：总结AI行业的整体发展方向
- **未来展望**：对AI技术和应用的未来发展进行预测

## Stagewise 开发工具集成

本项目已集成 stagewise 开发工具，为Web界面提供AI驱动的编辑功能。这个工具就像一个智能助手，可以在你开发网页的时候帮助你修改代码。

### Stagewise 是什么？

Stagewise 是一个强大的开发工具，它通过在你的网页上显示一个工具栏，让你能够直接在浏览器中与AI代理互动，从而更方便地修改和优化你的代码。你可以把它想象成一个“智能画笔”，当你看到网页上的某个部分不满意时，可以直接用这个画笔“告诉”AI代理你想怎么改，AI代理就会帮你修改对应的代码。

### Stagewise 如何帮助你？

- 🔧 **浏览器工具栏**：在你的网页上会有一个小工具栏，它连接着一个“AI大脑”（AI代理）和你的代码编辑器。
- 🎯 **选择页面元素并留下评论**：你可以直接在网页上点击任何你想要修改的部分（比如一个按钮、一段文字），然后用简单的语言告诉AI代理你想要做什么样的改变。AI代理会根据你的描述和当前代码的上下文来理解你的意图。
- 🚀 **AI驱动的修改**：AI代理会根据你的指令，自动分析相关的代码，并尝试为你生成或修改代码，以实现你想要的效果。这大大简化了代码修改的过程，即使你不懂复杂的编程语言，也能通过简单的对话来“指挥”AI修改代码。
- 💡 **“roo code”的可能含义**：你提到的“roo code”可能指的是通过 Stagewise 工具生成的或与 Stagewise 交互时涉及到的代码。Stagewise 的目标就是让代码的修改变得像“搭积木”一样简单，你只需要告诉它你想要什么，它就会帮你“搭”出对应的代码。

### 使用方法

1. **开发模式**（stagewise工具栏已启用）：
   ```bash
   npm run dev
   # 访问 http://localhost:3000
   ```

2. **生产模式**（stagewise工具栏已禁用）：
   ```bash
   npm run serve:prod
   # 访问 http://localhost:8080
   ```

### 技术实现
- 使用 `@stagewise/toolbar` 包进行框架无关的集成
- 通过检测 `localhost`、端口号或 `file:` 协议自动识别开发环境
- 使用 ES 模块动态导入，确保生产环境下不加载工具栏代码

## 未来计划

- 支持更多AI资讯来源网站
- 添加定时自动采集功能
- 实现资讯分类和标签功能
- 提供Web界面，方便用户交互
- 增强分析能力，提供更深入的行业洞察和可视化图表

## 注意事项

- 本工具仅用于学习和研究目的，请勿用于商业用途
- 请遵守相关网站的使用条款和robots.txt规定
- 采集的内容版权归原作者所有，请注明来源