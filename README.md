# AI资讯收集与分析工具

## 项目简介

这是一个使用Playwright自动化工具收集并分析AI行业最新资讯的项目。该工具可以自动访问AI资讯网站，提取最新的AI相关新闻、产品发布、融资信息等，并将其整理成结构化的Markdown文档，同时提供深度分析报告，方便用户快速了解AI行业动态和发展趋势。

## 功能特点

- **自动化数据采集**：使用Playwright自动访问AI资讯网站，无需手动操作
- **内容智能提取**：自动识别并提取最新的AI资讯内容，包括标题、正文和来源
- **结构化输出**：将采集的资讯整理成规范的Markdown格式，便于阅读和分享
- **深度趋势分析**：对采集的资讯进行背景、原因、影响和发展趋势的结构化分析
- **定制化采集**：可以根据需要调整采集的资讯数量和类型

## 使用方法

### 前提条件

- 安装了Node.js环境
- 安装了Playwright库
- 配置了MCP (Model Control Protocol) 环境

### 基本使用

1. 克隆或下载本项目到本地
2. 在项目根目录下运行以下命令：

```
# 采集最新的AI资讯并保存到Markdown文件
# 示例命令（实际命令可能因环境而异）
```

3. 执行完成后，将在项目目录下生成`AI资讯汇总.md`文件，包含最新的AI资讯内容
4. 同时生成`AI资讯分析报告.md`文件，提供对资讯的深度分析和趋势洞察

### 自定义配置

可以通过修改参数来自定义资讯采集：

- **资讯数量**：可以调整采集的资讯数量，默认为5篇
- **资讯来源**：目前默认从AI工具集网站采集，可以扩展支持其他来源
- **输出格式**：可以调整输出的Markdown格式，满足不同的展示需求
- **分析维度**：可以自定义分析报告的维度，如技术、市场、投资等

## 项目结构

```
├── README.md                 # 项目说明文档
├── AI资讯汇总.md             # 生成的AI资讯汇总文件
├── AI资讯分析报告.md         # 生成的AI资讯深度分析报告
```

## 分析报告内容

分析报告采用结构化思维方法，包含以下主要部分：

- **个案分析**：对每篇资讯的背景、原因、影响和发展趋势进行深入分析
- **横向对比**：通过对比不同资讯，发现共同趋势和规律
- **整体趋势**：总结AI行业的整体发展方向
- **未来展望**：对AI技术和应用的未来发展进行预测

## 未来计划

- 支持更多AI资讯来源网站
- 添加定时自动采集功能
- 实现资讯分类和标签功能
- 提供Web界面，方便用户交互
- 增强分析能力，提供更深入的行业洞察和可视化图表

## 注意事项

- 本工具仅用于学习和研究目的，请勿用于商业用途
- 请遵守相关网站的使用条款和robots.txt规定
- 采集的内容版权归原作者所有，请注明来源