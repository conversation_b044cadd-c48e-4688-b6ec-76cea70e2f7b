import React from 'react'

const Trends = () => {
  const generalTrends = [
    {
      icon: "bi-graph-up",
      iconColor: "trend-up",
      title: "多模态融合成为主流",
      content: "AI系统正从单一模态（如纯文本或纯图像）向多模态融合方向发展，能够同时处理文本、图像、语音和视频等多种数据类型，并在不同模态间进行信息转换和整合。这种融合使AI系统能够更全面地理解和处理复杂信息，提供更自然的人机交互体验。"
    },
    {
      icon: "bi-graph-up",
      iconColor: "trend-up",
      title: "边缘AI与云端协同计算",
      content: "随着边缘计算技术的发展，越来越多的AI处理任务从云端转移到本地设备上执行，实现低延迟响应和更好的隐私保护。同时，复杂任务仍依赖云端强大的计算资源，形成边缘-云协同的混合计算模式，平衡性能、功耗和隐私需求。"
    },
    {
      icon: "bi-graph-up-arrow",
      iconColor: "trend-neutral",
      title: "AI个性化与定制化",
      content: "通用AI模型正逐渐向个性化和定制化方向发展，能够根据用户的使用习惯、偏好和需求提供差异化服务。这种趋势在语音助手、推荐系统和内容创作工具中尤为明显，用户可以通过少量示例数据训练出适合自己需求的AI模型。"
    }
  ]

  const futureOutlook = [
    {
      icon: "bi-lightning",
      iconColor: "trend-up",
      title: "AI硬件形态多样化",
      content: "未来2-3年内，AI硬件将呈现形态多样化趋势，从传统的智能手机、电脑扩展到AR/VR眼镜、可穿戴设备、智能家居等多种形态。这些设备将更加注重与人类自然交互，减少对屏幕的依赖，提供更加无缝的AI辅助体验。"
    },
    {
      icon: "bi-lightning",
      iconColor: "trend-up",
      title: "垂直领域AI应用深化",
      content: "通用AI技术将进一步向垂直领域渗透，特别是在医疗、教育、金融和法律等专业领域，结合领域知识和数据开发更加专业化的AI解决方案。这些垂直应用将从辅助工具逐步发展为核心生产力工具，重塑行业工作流程和服务模式。"
    },
    {
      icon: "bi-exclamation-triangle",
      iconColor: "trend-down",
      title: "AI监管与伦理挑战",
      content: "随着AI技术的广泛应用，相关的监管框架和伦理标准将成为行业发展的重要议题。隐私保护、算法透明度、数据安全和责任归属等问题需要技术和政策层面的共同解决，平衡创新与风险控制的关系将是未来AI发展的关键挑战。"
    }
  ]

  const TrendItem = ({ icon, iconColor, title, content }) => (
    <div className="trend-item">
      <h5>
        <i className={`bi ${icon} trend-icon ${iconColor}`}></i>
        {title}
      </h5>
      <p>{content}</p>
    </div>
  )

  return (
    <div className="tab-pane fade show active">
      <div className="row">
        {/* 总体趋势 */}
        <div className="col-md-6 animate-on-scroll">
          <div className="card mb-4">
            <div className="card-header bg-primary text-white">
              <h5 className="card-title mb-0">总体趋势</h5>
            </div>
            <div className="card-body">
              {generalTrends.map((trend, index) => (
                <TrendItem key={index} {...trend} />
              ))}
            </div>
          </div>
        </div>

        {/* 未来展望 */}
        <div className="col-md-6 animate-on-scroll">
          <div className="card mb-4">
            <div className="card-header bg-info text-white">
              <h5 className="card-title mb-0">未来展望</h5>
            </div>
            <div className="card-body">
              {futureOutlook.map((trend, index) => (
                <TrendItem key={index} {...trend} />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Trends
