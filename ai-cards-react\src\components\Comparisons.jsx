import React from 'react'

const Comparisons = () => {
  const voiceComparisonData = [
    {
      product: "MiniMax Audio",
      company: "MiniMax",
      languages: "40+种语言",
      latency: "200ms",
      voiceCloning: "✓ (3分钟样本)",
      emotionRecognition: "✓",
      applications: "教育、客服"
    },
    {
      product: "Whisper Pro",
      company: "OpenAI",
      languages: "100+种语言",
      latency: "500ms",
      voiceCloning: "✗",
      emotionRecognition: "部分支持",
      applications: "内容创作、字幕"
    },
    {
      product: "Galaxy Voice",
      company: "三星",
      languages: "28种语言",
      latency: "150ms",
      voiceCloning: "✓ (5分钟样本)",
      emotionRecognition: "✓",
      applications: "智能助手、IoT控制"
    },
    {
      product: "讯飞听见",
      company: "科大讯飞",
      languages: "中文+12种语言",
      latency: "100ms",
      voiceCloning: "✓ (10分钟样本)",
      emotionRecognition: "✓",
      applications: "会议记录、字幕"
    }
  ]

  const researchComparisonData = [
    {
      product: "Kimi-Researcher",
      company: "月之暗面",
      database: "1亿+论文",
      analysis: "深度分析",
      trendPrediction: "✓",
      collaboration: "有限",
      pricing: "订阅制"
    },
    {
      product: "Elicit",
      company: "Ought",
      database: "2亿+论文",
      analysis: "摘要提取",
      trendPrediction: "部分支持",
      collaboration: "✓",
      pricing: "免费+高级版"
    },
    {
      product: "Semantic Scholar",
      company: "AI2",
      database: "2亿+论文",
      analysis: "引用分析",
      trendPrediction: "✗",
      collaboration: "✗",
      pricing: "免费"
    },
    {
      product: "ResearchRabbit",
      company: "ResearchRabbit Inc",
      database: "1.5亿+论文",
      analysis: "关系网络",
      trendPrediction: "✓",
      collaboration: "✓",
      pricing: "免费+机构版"
    }
  ]

  const hardwareComparisonData = [
    {
      product: "Meta/Oakley AI眼镜",
      company: "Meta & Oakley",
      type: "AR眼镜",
      weight: "78g",
      battery: "8小时",
      aiCapability: "边缘计算",
      price: "$699-899"
    },
    {
      product: "Apple Vision Pro",
      company: "Apple",
      type: "MR头显",
      weight: "650g",
      battery: "2小时",
      aiCapability: "高性能",
      price: "$3499+"
    },
    {
      product: "Humane AI Pin",
      company: "Humane",
      type: "可穿戴徽章",
      weight: "55g",
      battery: "4小时",
      aiCapability: "云端+边缘",
      price: "$699+订阅"
    },
    {
      product: "Rabbit R1",
      company: "Rabbit Inc",
      type: "AI掌上设备",
      weight: "115g",
      battery: "6小时",
      aiCapability: "云端处理",
      price: "$199"
    }
  ]

  const ComparisonTable = ({ title, data, headers, headerColor }) => (
    <div className="col-12 mb-4 animate-on-scroll">
      <div className="card">
        <div className={`card-header ${headerColor}`}>
          <h5 className="card-title mb-0">{title}</h5>
        </div>
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-hover">
              <thead>
                <tr>
                  {headers.map((header, index) => (
                    <th key={index}>{header}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {data.map((row, index) => (
                  <tr key={index}>
                    {Object.values(row).map((value, cellIndex) => (
                      <td key={cellIndex}>{value}</td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="tab-pane fade show active">
      <div className="row">
        <ComparisonTable
          title="AI语音技术对比"
          data={voiceComparisonData}
          headers={["产品名称", "开发公司", "语言支持", "实时延迟", "语音克隆", "情感识别", "主要应用场景"]}
          headerColor="bg-primary text-white"
        />
        
        <ComparisonTable
          title="研究助手工具对比"
          data={researchComparisonData}
          headers={["产品名称", "开发公司", "数据库覆盖", "文献分析", "趋势预测", "协作功能", "定价模式"]}
          headerColor="bg-success text-white"
        />
        
        <ComparisonTable
          title="AI硬件设备对比"
          data={hardwareComparisonData}
          headers={["产品名称", "开发公司", "设备类型", "重量", "电池续航", "AI处理能力", "价格区间"]}
          headerColor="bg-dark text-white"
        />
      </div>
    </div>
  )
}

export default Comparisons
