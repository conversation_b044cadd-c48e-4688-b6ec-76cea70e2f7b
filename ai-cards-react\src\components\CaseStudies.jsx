import React, { useState } from 'react'
import CaseCard from './CaseCard'

const CaseStudies = ({ searchTerm }) => {
  const [activeFilter, setActiveFilter] = useState('all')

  const caseData = [
    {
      id: 1,
      title: "MiniMax Audio",
      category: "voice",
      tags: ["语音技术", "多语言支持"],
      tagColors: ["bg-info text-white", "bg-secondary text-white"],
      headerColor: "bg-primary text-white",
      description: "MiniMax推出的新一代语音大模型，支持多语言实时翻译和语音克隆功能，可应用于客服、教育等多个领域。",
      features: [
        "超低延迟的实时语音识别",
        "支持40+种语言互译", 
        "个性化语音克隆（3分钟样本）"
      ],
      expandedContent: [
        "MiniMax Audio采用了最新的Transformer-XL架构，通过自监督学习方法在超过10万小时的多语言语音数据上进行预训练。其语音克隆技术只需3分钟的语音样本即可复制说话者的音色和语调特征，同时保持自然的语音韵律。",
        "在实际应用中，MiniMax Audio已被多家在线教育平台采用，用于提供实时口语评估和发音纠正服务。同时，其在客服领域的应用也显著提升了多语言服务能力和用户满意度。"
      ],
      releaseDate: "2023年11月",
      buttonColor: "btn-outline-primary"
    },
    {
      id: 2,
      title: "Kimi-Researcher",
      category: "research",
      tags: ["研究助手", "学术工具"],
      tagColors: ["bg-success text-white", "bg-warning text-dark"],
      headerColor: "bg-success text-white",
      description: "月之暗面推出的AI学术研究助手，能够检索、分析和总结学术文献，辅助研究人员进行文献综述和研究方向探索。",
      features: [
        "支持多种学术数据库检索",
        "论文关键信息提取与总结",
        "研究趋势分析与可视化"
      ],
      expandedContent: [
        "Kimi-Researcher基于大规模语言模型和知识图谱技术，能够理解复杂的学术概念和专业术语。系统已收录超过1亿篇学术论文的元数据，覆盖自然科学、社会科学、工程技术等多个领域。",
        "用户可以通过自然语言描述研究问题，系统会自动检索相关文献，提取关键方法、结果和结论，并生成结构化的文献综述报告。同时，其趋势分析功能可以帮助研究人员发现新兴研究方向和潜在合作机会。"
      ],
      releaseDate: "2023年12月",
      buttonColor: "btn-outline-success"
    },
    {
      id: 3,
      title: "Galaxy General",
      category: "research,voice",
      tags: ["语音技术", "研究助手"],
      tagColors: ["bg-info text-white", "bg-success text-white"],
      headerColor: "bg-info text-white",
      description: "三星推出的多模态AI助手，整合语音交互和知识检索功能，为用户提供智能化的信息服务和任务协助。",
      features: [
        "多模态输入（语音、文本、图像）",
        "个性化知识库构建",
        "跨设备协同工作"
      ],
      expandedContent: [
        "Galaxy General采用了三星自研的多模态融合技术，能够同时处理语音、文本和图像输入，并在不同模态间进行信息转换和整合。系统支持用户构建个人知识库，可以学习用户的使用习惯和偏好，提供更加个性化的服务。",
        "在三星生态系统内，Galaxy General可以跨手机、平板、电视和智能家居设备无缝协作，实现统一的用户体验和数据同步。其语音交互系统支持自然对话和上下文理解，能够处理复杂的多轮对话和任务指令。"
      ],
      releaseDate: "2024年1月",
      buttonColor: "btn-outline-info"
    }
  ]

  const filterButtons = [
    { key: 'all', label: '全部' },
    { key: 'voice', label: '语音技术' },
    { key: 'research', label: '研究助手' },
    { key: 'hardware', label: '硬件设备' },
    { key: 'medical', label: '医疗应用' }
  ]

  const filteredCases = caseData.filter(caseItem => {
    const matchesFilter = activeFilter === 'all' || caseItem.category.includes(activeFilter)
    const matchesSearch = searchTerm === '' || 
      caseItem.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      caseItem.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      caseItem.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    
    return matchesFilter && matchesSearch
  })

  return (
    <div className="tab-pane fade show active">
      {/* 过滤按钮 */}
      <div className="mb-4">
        <div className="btn-group">
          {filterButtons.map(button => (
            <button
              key={button.key}
              className={`btn btn-outline-primary filter-btn ${activeFilter === button.key ? 'active' : ''}`}
              onClick={() => setActiveFilter(button.key)}
            >
              {button.label}
            </button>
          ))}
        </div>
      </div>

      {/* 案例卡片 */}
      <div className="row">
        {filteredCases.map(caseItem => (
          <div key={caseItem.id} className="col-md-6 col-lg-4 mb-4">
            <CaseCard {...caseItem} />
          </div>
        ))}
      </div>

      {filteredCases.length === 0 && (
        <div className="text-center py-5">
          <p className="text-muted">没有找到匹配的案例</p>
        </div>
      )}
    </div>
  )
}

export default CaseStudies
